{"rustc": 16591470773350601817, "features": "[\"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 5796931310894148030, "profile": 2241668132362809309, "path": 204913056743982447, "deps": [[555019317135488525, "regex_automata", false, 8137269288095226071], [2779309023524819297, "aho_corasick", false, 17130622281034218809], [9408802513701742484, "regex_syntax", false, 16502042904441010684], [15932120279885307830, "memchr", false, 7623046965225097316]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\regex-e55187641eefbc66\\dep-lib-regex", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}