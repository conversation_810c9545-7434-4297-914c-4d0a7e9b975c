{"rustc": 16591470773350601817, "features": "[\"alloc\", \"derive\", \"yoke\"]", "declared_features": "[\"alloc\", \"databake\", \"derive\", \"hashmap\", \"serde\", \"std\", \"yoke\"]", "target": 1825474209729987087, "profile": 2241668132362809309, "path": 7660247352603645322, "deps": [[9620753569207166497, "zerovec_derive", false, 94551957084603546], [10706449961930108323, "yoke", false, 4108046247233183081], [17046516144589451410, "zerofrom", false, 9741891236129018316]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\zerovec-dddedfa50b5e3b98\\dep-lib-zerovec", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}