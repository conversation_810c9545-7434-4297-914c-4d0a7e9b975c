use crate::input::protocol::{get_current_timestamp, InputProtocol};
use crate::types::{<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>E<PERSON>, Mouse<PERSON><PERSON>};
use anyhow::Result;
use enigo::{<PERSON>igo, MouseControllable};
use log::{debug, error, info, warn};
use rdev::{listen, Event, EventType};
use std::sync::mpsc::{self, Receiver, Sender};
use std::sync::{Arc, Mutex};
use std::thread;
use std::time::Duration;

pub struct MouseInputHandler {
    input_protocol: Arc<Mutex<InputProtocol>>,
    capture_enabled: Arc<Mutex<bool>>,
    event_sender: Option<Sender<MouseEvent>>,
    button_sender: Option<Sender<MouseButtonEvent>>,
    _listener_thread: Option<thread::JoinHandle<()>>,
}

impl MouseInputHandler {
    pub fn new() -> Self {
        Self {
            input_protocol: Arc::new(Mutex::new(InputProtocol::new())),
            capture_enabled: Arc::new(Mutex::new(false)),
            event_sender: None,
            button_sender: None,
            _listener_thread: None,
        }
    }

    pub fn connect_to_device(&mut self, target_ip: std::net::IpAddr, target_port: u16) -> Result<()> {
        info!("Connecting mouse input handler to {}:{}", target_ip, target_port);
        
        let mut protocol = self.input_protocol.lock().unwrap();
        protocol.connect(target_ip, target_port)?;
        
        // Start input processing threads
        self.start_input_processing()?;
        
        info!("Mouse input handler connected successfully");
        Ok(())
    }

    pub fn disconnect(&mut self) {
        info!("Disconnecting mouse input handler");
        
        self.set_capture_enabled(false);
        
        let mut protocol = self.input_protocol.lock().unwrap();
        protocol.disconnect();
        
        // Clean up senders
        self.event_sender = None;
        self.button_sender = None;
    }

    pub fn set_capture_enabled(&self, enabled: bool) {
        info!("Setting mouse capture enabled: {}", enabled);
        let mut capture_enabled = self.capture_enabled.lock().unwrap();
        *capture_enabled = enabled;
    }

    pub fn is_capture_enabled(&self) -> bool {
        *self.capture_enabled.lock().unwrap()
    }

    pub fn is_connected(&self) -> bool {
        let protocol = self.input_protocol.lock().unwrap();
        protocol.is_connected()
    }

    pub fn send_test_mouse_movement(&self) -> Result<()> {
        if !self.is_connected() {
            return Err(anyhow::anyhow!("Not connected to device"));
        }

        info!("Sending test mouse movements");

        let test_movements = vec![
            (10, 0),   // Right
            (0, 10),   // Down
            (-10, 0),  // Left
            (0, -10),  // Up
            (5, 5),    // Diagonal
            (-5, -5),  // Diagonal back
        ];

        let mut protocol = self.input_protocol.lock().unwrap();
        
        for (dx, dy) in test_movements {
            let event = MouseEvent {
                delta_x: dx,
                delta_y: dy,
                timestamp: get_current_timestamp(),
            };
            
            if let Err(e) = protocol.send_mouse_move(event) {
                error!("Failed to send test mouse movement: {}", e);
                return Err(e);
            }
            
            // Small delay between movements
            drop(protocol);
            thread::sleep(Duration::from_millis(100));
            protocol = self.input_protocol.lock().unwrap();
        }

        info!("Test mouse movements sent successfully");
        Ok(())
    }

    pub fn send_test_mouse_click(&self) -> Result<()> {
        if !self.is_connected() {
            return Err(anyhow::anyhow!("Not connected to device"));
        }

        info!("Sending test mouse click");

        let mut protocol = self.input_protocol.lock().unwrap();
        
        // Send left button press
        let press_event = MouseButtonEvent {
            button: MouseButton::Left,
            action: ButtonAction::Press,
            timestamp: get_current_timestamp(),
        };
        protocol.send_mouse_button(press_event)?;

        // Small delay
        drop(protocol);
        thread::sleep(Duration::from_millis(50));
        protocol = self.input_protocol.lock().unwrap();

        // Send left button release
        let release_event = MouseButtonEvent {
            button: MouseButton::Left,
            action: ButtonAction::Release,
            timestamp: get_current_timestamp(),
        };
        protocol.send_mouse_button(release_event)?;

        info!("Test mouse click sent successfully");
        Ok(())
    }

    fn start_input_processing(&mut self) -> Result<()> {
        let (event_tx, event_rx) = mpsc::channel();
        let (button_tx, button_rx) = mpsc::channel();

        self.event_sender = Some(event_tx);
        self.button_sender = Some(button_tx);

        // Start mouse event processing thread
        let protocol_clone = Arc::clone(&self.input_protocol);
        thread::spawn(move || {
            Self::process_mouse_events(protocol_clone, event_rx);
        });

        // Start mouse button processing thread
        let protocol_clone = Arc::clone(&self.input_protocol);
        thread::spawn(move || {
            Self::process_mouse_buttons(protocol_clone, button_rx);
        });

        Ok(())
    }

    fn process_mouse_events(
        protocol: Arc<Mutex<InputProtocol>>,
        receiver: Receiver<MouseEvent>,
    ) {
        while let Ok(event) = receiver.recv() {
            if let Ok(mut protocol) = protocol.lock() {
                if let Err(e) = protocol.send_mouse_move(event) {
                    error!("Failed to send mouse move event: {}", e);
                }
            }
        }
    }

    fn process_mouse_buttons(
        protocol: Arc<Mutex<InputProtocol>>,
        receiver: Receiver<MouseButtonEvent>,
    ) {
        while let Ok(event) = receiver.recv() {
            if let Ok(mut protocol) = protocol.lock() {
                if let Err(e) = protocol.send_mouse_button(event) {
                    error!("Failed to send mouse button event: {}", e);
                }
            }
        }
    }

    // Manual mouse movement injection (for testing)
    pub fn inject_mouse_movement(&self, delta_x: i16, delta_y: i16) -> Result<()> {
        if let Some(sender) = &self.event_sender {
            let event = MouseEvent {
                delta_x,
                delta_y,
                timestamp: get_current_timestamp(),
            };
            
            sender.send(event)
                .map_err(|e| anyhow::anyhow!("Failed to send mouse event: {}", e))?;
        }
        Ok(())
    }

    pub fn inject_mouse_button(&self, button: MouseButton, action: ButtonAction) -> Result<()> {
        if let Some(sender) = &self.button_sender {
            let event = MouseButtonEvent {
                button,
                action,
                timestamp: get_current_timestamp(),
            };
            
            sender.send(event)
                .map_err(|e| anyhow::anyhow!("Failed to send mouse button event: {}", e))?;
        }
        Ok(())
    }
}

impl Default for MouseInputHandler {
    fn default() -> Self {
        Self::new()
    }
}
