{"rustc": 16591470773350601817, "features": "[\"bytemuck\", \"default_fonts\", \"log\"]", "declared_features": "[\"bytemuck\", \"cint\", \"color-hex\", \"deadlock_detection\", \"default\", \"default_fonts\", \"document-features\", \"extra_asserts\", \"extra_debug_asserts\", \"log\", \"mint\", \"serde\", \"unity\"]", "target": 10495837225410426609, "profile": 2241668132362809309, "path": 8008017034586534883, "deps": [[966925859616469517, "ahash", false, 16468360920981511885], [2243726711235319235, "ab_glyph", false, 15354006824338657627], [3955665883727284124, "ecolor", false, 4669200282314703898], [4495526598637097934, "parking_lot", false, 1198172675024759262], [5931649091606299019, "nohash_hasher", false, 9309983683178885789], [5986029879202738730, "log", false, 1928244995240463600], [6511429716036861196, "bytemuck", false, 15639968703134488806], [10104497354789709585, "emath", false, 2311489455343842340]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\epaint-28e16cf3cd0b4954\\dep-lib-epaint", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}