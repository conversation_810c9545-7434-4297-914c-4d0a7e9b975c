use crate::gui::app::GameStreamApp;
use crate::types::{DeviceState, GameStreamDevice, PairState};
use eframe::egui;

pub fn show_device_panel(
    ui: &mut egui::Ui,
    discovered_devices: &mut Vec<GameStreamDevice>,
    selected_device: &mut Option<GameStreamDevice>,
    scanning: &mut bool,
    scan_status: &mut String,
    pairing_pin: &mut String,
    pairing_in_progress: &mut bool,
    connection_status: &mut String,
    app: &mut GameStreamApp,
) {
    ui.group(|ui| {
        ui.heading("Device Discovery");

        ui.horizontal(|ui| {
            if ui.button("Scan for Devices").clicked() && !*scanning {
                app.scan_for_devices();
            }

            if *scanning {
                ui.spinner();
            }

            ui.label(scan_status.as_str());
        });

        ui.separator();

        // Device list
        if discovered_devices.is_empty() {
            ui.label("No devices found. Click 'Scan for Devices' to search.");
        } else {
            ui.label(format!("Found {} device(s):", discovered_devices.len()));

            for device in discovered_devices.iter() {
                ui.group(|ui| {
                    ui.horizontal(|ui| {
                        ui.vertical(|ui| {
                            ui.strong(&device.name);
                            ui.label(format!("Address: {}", device.address));
                            ui.label(format!("UUID: {}", device.uuid));
                            
                            // Device state indicator
                            let (state_text, state_color) = match device.state {
                                DeviceState::Online => ("Online", egui::Color32::GREEN),
                                DeviceState::Offline => ("Offline", egui::Color32::RED),
                                DeviceState::Unknown => ("Unknown", egui::Color32::GRAY),
                            };
                            ui.colored_label(state_color, format!("Status: {}", state_text));

                            // Pair state indicator
                            let (pair_text, pair_color) = match device.pair_state {
                                PairState::Paired => ("Paired", egui::Color32::GREEN),
                                PairState::NotPaired => ("Not Paired", egui::Color32::YELLOW),
                                PairState::PinWrong => ("Wrong PIN", egui::Color32::RED),
                                PairState::Failed => ("Pairing Failed", egui::Color32::RED),
                                PairState::Unknown => ("Unknown", egui::Color32::GRAY),
                            };
                            ui.colored_label(pair_color, format!("Pairing: {}", pair_text));
                        });

                        ui.separator();

                        ui.vertical(|ui| {
                            // Connection controls
                            if device.pair_state == PairState::Paired {
                                if ui.button("Connect").clicked() {
                                    app.connect_to_device(device.clone());
                                }
                            } else {
                                ui.label("Pairing required");
                                
                                ui.horizontal(|ui| {
                                    ui.label("PIN:");
                                    ui.text_edit_singleline(pairing_pin);
                                });

                                ui.horizontal(|ui| {
                                    if ui.button("Pair").clicked() && !pairing_pin.is_empty() && !*pairing_in_progress {
                                        app.pair_with_device(device.clone(), pairing_pin);
                                    }

                                    if *pairing_in_progress {
                                        ui.spinner();
                                        ui.label("Pairing...");
                                    }
                                });
                            }

                            // Show current game if any
                            if let Some(game_id) = device.current_game_id {
                                ui.colored_label(
                                    egui::Color32::BLUE,
                                    format!("Running game ID: {}", game_id)
                                );
                            }
                        });
                    });
                });
                ui.add_space(5.0);
            }
        }

        ui.separator();

        // Connection status
        ui.horizontal(|ui| {
            ui.label("Connection Status:");
            
            let (status_color, status_icon) = if connection_status.contains("Connected") {
                (egui::Color32::GREEN, "🟢")
            } else if connection_status.contains("Connecting") {
                (egui::Color32::YELLOW, "🟡")
            } else {
                (egui::Color32::RED, "🔴")
            };
            
            ui.colored_label(status_color, format!("{} {}", status_icon, connection_status));
        });

        // Disconnect button
        if selected_device.is_some() && connection_status.contains("Connected") {
            if ui.button("Disconnect").clicked() {
                app.disconnect_from_device();
            }
        }
    });
}
