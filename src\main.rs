use anyhow::Result;
use log::info;

mod discovery;
mod gamestream;
mod gui;
mod input;
mod types;

use gui::GameStreamApp;

#[tokio::main]
async fn main() -> Result<()> {
    env_logger::init();
    info!("Starting GameStream Mouse Controller");

    let options = eframe::NativeOptions {
        viewport: egui::ViewportBuilder::default().with_inner_size([800.0, 600.0]),
        ..Default::default()
    };

    eframe::run_native(
        "GameStream Mouse Controller",
        options,
        Box::new(|_cc| Box::new(GameStreamApp::new())),
    )
    .map_err(|e| anyhow::anyhow!("Failed to run GUI: {}", e))?;

    Ok(())
}
