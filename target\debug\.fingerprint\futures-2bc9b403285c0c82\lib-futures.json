{"rustc": 16591470773350601817, "features": "[\"alloc\", \"async-await\", \"default\", \"executor\", \"futures-executor\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"bilock\", \"cfg-target-has-atomic\", \"compat\", \"default\", \"executor\", \"futures-executor\", \"io-compat\", \"std\", \"thread-pool\", \"unstable\", \"write-all-vectored\"]", "target": 7465627196321967167, "profile": 17467636112133979524, "path": 11434820296197482439, "deps": [[5103565458935487, "futures_io", false, 7973003275798185917], [1811549171721445101, "futures_channel", false, 14516987446170600804], [7013762810557009322, "futures_sink", false, 8266735578287700119], [7620660491849607393, "futures_core", false, 2837511519693940984], [10629569228670356391, "futures_util", false, 17093309161738785981], [12779779637805422465, "futures_executor", false, 18373819793349904130], [16240732885093539806, "futures_task", false, 3833019813322648339]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\futures-2bc9b403285c0c82\\dep-lib-futures", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}