use crate::types::{<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>};
use anyhow::{anyhow, Result};
use log::{debug, error, info};
use std::net::{SocketAddr, UdpSocket};
use std::time::{SystemTime, UNIX_EPOCH};

// GameStream input protocol constants
const INPUT_PACKET_TYPE_MOUSE_MOVE: u8 = 0x06;
const INPUT_PACKET_TYPE_MOUSE_BUTTON: u8 = 0x07;

// Mouse button constants (matching Limelight protocol)
const BUTTON_LEFT: u8 = 0x01;
const BUTTON_MIDDLE: u8 = 0x02;
const BUTTON_RIGHT: u8 = 0x03;
const BUTTON_X1: u8 = 0x04;
const BUTTON_X2: u8 = 0x05;

const BUTTON_ACTION_PRESS: u8 = 0x07;
const BUTTON_ACTION_RELEASE: u8 = 0x08;

pub struct InputProtocol {
    socket: Option<UdpSocket>,
    target_address: Option<SocketAddr>,
    sequence_number: u32,
}

impl InputProtocol {
    pub fn new() -> Self {
        Self {
            socket: None,
            target_address: None,
            sequence_number: 0,
        }
    }

    pub fn connect(&mut self, target_ip: std::net::IpAddr, target_port: u16) -> Result<()> {
        info!("Connecting input protocol to {}:{}", target_ip, target_port);

        // Bind to any available local port
        let socket = UdpSocket::bind("0.0.0.0:0")?;
        socket.set_nonblocking(false)?;

        let target_address = SocketAddr::new(target_ip, target_port);
        
        self.socket = Some(socket);
        self.target_address = Some(target_address);
        self.sequence_number = 0;

        info!("Input protocol connected successfully");
        Ok(())
    }

    pub fn disconnect(&mut self) {
        info!("Disconnecting input protocol");
        self.socket = None;
        self.target_address = None;
        self.sequence_number = 0;
    }

    pub fn send_mouse_move(&mut self, event: MouseEvent) -> Result<()> {
        if self.socket.is_none() || self.target_address.is_none() {
            return Err(anyhow!("Input protocol not connected"));
        }

        debug!("Sending mouse move: dx={}, dy={}", event.delta_x, event.delta_y);

        let packet = self.create_mouse_move_packet(event)?;
        self.send_packet(&packet)?;

        Ok(())
    }

    pub fn send_mouse_button(&mut self, event: MouseButtonEvent) -> Result<()> {
        if self.socket.is_none() || self.target_address.is_none() {
            return Err(anyhow!("Input protocol not connected"));
        }

        debug!("Sending mouse button: {:?} {:?}", event.button, event.action);

        let packet = self.create_mouse_button_packet(event)?;
        self.send_packet(&packet)?;

        Ok(())
    }

    fn create_mouse_move_packet(&mut self, event: MouseEvent) -> Result<Vec<u8>> {
        let mut packet = Vec::with_capacity(16);

        // Packet header
        packet.push(INPUT_PACKET_TYPE_MOUSE_MOVE);
        packet.extend_from_slice(&self.sequence_number.to_le_bytes());
        self.sequence_number = self.sequence_number.wrapping_add(1);

        // Timestamp
        let timestamp = event.timestamp;
        packet.extend_from_slice(&timestamp.to_le_bytes());

        // Mouse delta values (little-endian)
        packet.extend_from_slice(&event.delta_x.to_le_bytes());
        packet.extend_from_slice(&event.delta_y.to_le_bytes());

        // Padding to align packet
        packet.push(0);

        Ok(packet)
    }

    fn create_mouse_button_packet(&mut self, event: MouseButtonEvent) -> Result<Vec<u8>> {
        let mut packet = Vec::with_capacity(16);

        // Packet header
        packet.push(INPUT_PACKET_TYPE_MOUSE_BUTTON);
        packet.extend_from_slice(&self.sequence_number.to_le_bytes());
        self.sequence_number = self.sequence_number.wrapping_add(1);

        // Timestamp
        let timestamp = event.timestamp;
        packet.extend_from_slice(&timestamp.to_le_bytes());

        // Button and action
        let button_code = match event.button {
            MouseButton::Left => BUTTON_LEFT,
            MouseButton::Right => BUTTON_RIGHT,
            MouseButton::Middle => BUTTON_MIDDLE,
            MouseButton::X1 => BUTTON_X1,
            MouseButton::X2 => BUTTON_X2,
        };

        let action_code = match event.action {
            ButtonAction::Press => BUTTON_ACTION_PRESS,
            ButtonAction::Release => BUTTON_ACTION_RELEASE,
        };

        packet.push(action_code);
        packet.push(button_code);

        // Padding
        packet.extend_from_slice(&[0u8; 2]);

        Ok(packet)
    }

    fn send_packet(&self, packet: &[u8]) -> Result<()> {
        if let (Some(socket), Some(target)) = (&self.socket, &self.target_address) {
            match socket.send_to(packet, target) {
                Ok(bytes_sent) => {
                    debug!("Sent {} bytes to {}", bytes_sent, target);
                    Ok(())
                }
                Err(e) => {
                    error!("Failed to send packet: {}", e);
                    Err(anyhow!("Failed to send packet: {}", e))
                }
            }
        } else {
            Err(anyhow!("Socket or target address not available"))
        }
    }

    pub fn is_connected(&self) -> bool {
        self.socket.is_some() && self.target_address.is_some()
    }

    pub fn get_target_address(&self) -> Option<SocketAddr> {
        self.target_address
    }
}

impl Default for InputProtocol {
    fn default() -> Self {
        Self::new()
    }
}

// Helper function to get current timestamp in milliseconds
pub fn get_current_timestamp() -> u64 {
    SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap_or_default()
        .as_millis() as u64
}
