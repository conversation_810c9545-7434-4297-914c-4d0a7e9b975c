# GameStream Mouse Controller

A Rust application that scans for NVIDIA GameStream devices on the local network and provides mouse input transmission without video display. This application is designed for scenarios where you only need to send mouse movements to a GameStream device without receiving the video stream.

## Features

- **Network Discovery**: Automatically scans for GameStream devices using mDNS
- **Device Pairing**: PIN-based pairing with GameStream devices
- **Game List**: Retrieves and displays available games from connected devices
- **Mouse Input**: Transmits mouse movements and clicks to the GameStream device
- **Test Functions**: Built-in test buttons for verifying mouse input transmission
- **GUI Interface**: User-friendly interface for device management and control

## Prerequisites

- Rust 1.70 or later
- Windows, macOS, or Linux
- NVIDIA GameStream-enabled device on the same network
- Network access to GameStream device (ports 47984/47989)

## Building

1. Clone or download this project
2. Navigate to the project directory
3. Build the application:

```bash
cargo build --release
```

## Running

```bash
cargo run
```

Or run the built executable:

```bash
./target/release/gamestream-mouse-controller
```

## Usage

### 1. Device Discovery

1. Click "Scan for Devices" to search for GameStream devices on your network
2. The application will display found devices with their status and pairing state

### 2. Device Pairing

1. For unpaired devices, enter the PIN displayed on the GameStream device
2. Click "Pair" to establish a connection
3. The device status will update to "Paired" when successful

### 3. Connecting to Device

1. Once paired, click "Connect" to establish a connection
2. The connection status will show "Connected" when successful

### 4. Game Management

1. Click "Refresh App List" to load available games from the connected device
2. Games will be displayed with their names and IDs
3. Click "Launch (Mouse Only)" to start a game in mouse-only mode

### 5. Mouse Input Control

1. Enable "Mouse Capture" to start transmitting mouse movements
2. Use test buttons to verify the connection:
   - "Test Mouse Movement": Sends predefined movement patterns
   - "Test Mouse Click": Sends a left mouse click
3. Use manual movement controls for precise testing
4. Advanced controls allow custom delta movements

## Configuration

The application uses standard GameStream ports:
- HTTP: 47989
- HTTPS: 47984
- Input: 47998 (UDP)

## Troubleshooting

### Device Not Found
- Ensure the GameStream device is on the same network
- Check that GameStream is enabled on the target device
- Verify firewall settings allow mDNS traffic

### Pairing Failed
- Ensure the PIN is entered correctly
- Check that no other Moonlight client is connected
- Restart the GameStream service on the target device

### Mouse Input Not Working
- Verify the device is properly connected
- Check that a game is running on the GameStream device
- Ensure mouse capture is enabled
- Test with the built-in test functions first

### Connection Issues
- Check network connectivity to the GameStream device
- Verify ports 47984 and 47989 are accessible
- Ensure no firewall is blocking the connection

## Technical Details

### Protocol Implementation

The application implements a subset of the NVIDIA GameStream protocol:

1. **Discovery**: Uses mDNS to find `_nvstream._tcp.local.` services
2. **Authentication**: PIN-based pairing with AES encryption
3. **Communication**: HTTPS for control, UDP for input transmission
4. **Input Protocol**: Compatible with Limelight input packet format

### Architecture

- **Discovery Module**: Handles mDNS scanning and device detection
- **GameStream Client**: Manages HTTP communication with devices
- **Pairing Manager**: Handles device pairing and authentication
- **Input Handler**: Captures and transmits mouse events
- **GUI**: egui-based user interface

## Limitations

- Video streaming is not implemented (mouse-only mode)
- Audio streaming is not supported
- Limited to mouse input (no keyboard or gamepad)
- Requires manual pairing for each device

## License

This project is provided as-is for educational and research purposes. Please respect NVIDIA's terms of service when using GameStream technology.

## Contributing

This is a demonstration project. For production use, consider:

- Implementing proper async/await patterns
- Adding comprehensive error handling
- Supporting additional input devices
- Implementing video streaming capabilities
- Adding configuration file support

## Acknowledgments

Based on the Moonlight project architecture and GameStream protocol implementation. Special thanks to the Moonlight development team for their open-source GameStream client implementation.
