{"rustc": 16591470773350601817, "features": "[\"alloc\", \"default\", \"dev_urandom_fallback\"]", "declared_features": "[\"alloc\", \"default\", \"dev_urandom_fallback\", \"less-safe-getrandom-custom-or-rdrand\", \"less-safe-getrandom-espidf\", \"slow_tests\", \"std\", \"test_logging\", \"unstable-testing-arm-no-hw\", \"unstable-testing-arm-no-neon\", \"wasm32_unknown_unknown_js\"]", "target": 13947150742743679355, "profile": 2241668132362809309, "path": 18348742803742534858, "deps": [[2828590642173593838, "cfg_if", false, 664146898130203983], [5491919304041016563, "build_script_build", false, 12248095001627476519], [8995469080876806959, "untrusted", false, 6505795147166412439], [9920160576179037441, "getrandom", false, 6584724732055500630]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\ring-8f6832f724b78d8a\\dep-lib-ring", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}