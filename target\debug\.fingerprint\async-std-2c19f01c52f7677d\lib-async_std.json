{"rustc": 16591470773350601817, "features": "[\"alloc\", \"async-attributes\", \"async-channel\", \"async-global-executor\", \"async-io\", \"async-lock\", \"async-process\", \"attributes\", \"crossbeam-utils\", \"default\", \"futures-channel\", \"futures-core\", \"futures-io\", \"futures-lite\", \"gloo-timers\", \"kv-log-macro\", \"log\", \"memchr\", \"once_cell\", \"pin-project-lite\", \"pin-utils\", \"slab\", \"std\", \"unstable\", \"wasm-bindgen-futures\"]", "declared_features": "[\"alloc\", \"async-attributes\", \"async-channel\", \"async-global-executor\", \"async-io\", \"async-lock\", \"async-process\", \"attributes\", \"crossbeam-utils\", \"default\", \"docs\", \"futures-channel\", \"futures-core\", \"futures-io\", \"futures-lite\", \"gloo-timers\", \"io_safety\", \"kv-log-macro\", \"log\", \"memchr\", \"once_cell\", \"pin-project-lite\", \"pin-utils\", \"slab\", \"std\", \"surf\", \"tokio02\", \"tokio03\", \"tokio1\", \"unstable\", \"wasm-bindgen-futures\"]", "target": 9139776409365598091, "profile": 2241668132362809309, "path": 11877401809418734266, "deps": [[5103565458935487, "futures_io", false, 7973003275798185917], [1615478164327904835, "pin_utils", false, 18306399723879971518], [1906322745568073236, "pin_project_lite", false, 15035029367069786925], [3722963349756955755, "once_cell", false, 4343645139131842619], [4468123440088164316, "crossbeam_utils", false, 11560983528621542842], [5195813957092839672, "async_lock", false, 17487201885255659114], [5302544599749092241, "async_channel", false, 16639426789214360274], [5451793922601807560, "slab", false, 2199129668425109446], [5986029879202738730, "log", false, 1928244995240463600], [7425331225454150061, "futures_lite", false, 3680112385922571074], [7620660491849607393, "futures_core", false, 2837511519693940984], [7912494907464980285, "async_process", false, 9790342837369093841], [9511937138168509053, "async_attributes", false, 13539879662283477658], [13330646740533913557, "async_global_executor", false, 1399536741699160411], [14383121809127811842, "async_io", false, 3554490526119496380], [15932120279885307830, "memchr", false, 7623046965225097316], [17569958903244628888, "kv_log_macro", false, 8143602385505199422]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\async-std-2c19f01c52f7677d\\dep-lib-async_std", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}