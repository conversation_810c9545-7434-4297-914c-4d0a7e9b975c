use crate::gamestream::xml::XmlParser;
use crate::types::{DeviceState, GameStreamApp, GameStreamDevice, PairState};
use anyhow::{anyhow, Result};
use log::{debug, error, info, warn};
use reqwest::{Client, ClientBuilder};
use std::time::Duration;
use uuid::Uuid;

pub struct GameStreamClient {
    client: Client,
    device: GameStreamDevice,
    unique_id: String,
}

impl GameStreamClient {
    pub fn new(device: GameStreamDevice) -> Result<Self> {
        let client = ClientBuilder::new()
            .timeout(Duration::from_secs(10))
            .danger_accept_invalid_certs(true) // GameStream uses self-signed certs
            .build()?;

        let unique_id = "0123456789ABCDEF".to_string(); // Common UID for Moonlight clients

        Ok(Self {
            client,
            device,
            unique_id,
        })
    }

    pub async fn get_server_info(&mut self) -> Result<()> {
        info!("Getting server info from {}", self.device.address);

        let url = format!(
            "https://{}:{}/serverinfo?uniqueid={}&uuid={}",
            self.device.address,
            self.device.https_port,
            self.unique_id,
            Uuid::new_v4().simple()
        );

        let response = self.client.get(&url).send().await?;
        let xml_content = response.text().await?;

        debug!("Server info XML: {}", xml_content);

        // Parse server info
        let parser = XmlParser::new();
        parser.verify_response_status(&xml_content)?;

        // Extract device information
        if let Some(hostname) = parser.get_xml_string(&xml_content, "hostname") {
            self.device.name = hostname;
        }

        if let Some(uuid) = parser.get_xml_string(&xml_content, "uniqueid") {
            self.device.uuid = uuid;
        }

        if let Some(mac) = parser.get_xml_string(&xml_content, "mac") {
            if mac != "00:00:00:00:00:00" {
                self.device.mac_address = Some(mac);
            }
        }

        // Check current game
        if let Some(current_game) = parser.get_xml_string(&xml_content, "currentgame") {
            self.device.current_game_id = current_game.parse().ok();
        }

        // Determine pair state
        if let Some(pair_status) = parser.get_xml_string(&xml_content, "PairStatus") {
            self.device.pair_state = match pair_status.as_str() {
                "1" => PairState::Paired,
                _ => PairState::NotPaired,
            };
        }

        self.device.state = DeviceState::Online;
        info!("Successfully retrieved server info for {}", self.device.name);

        Ok(())
    }

    pub async fn get_app_list(&self) -> Result<Vec<GameStreamApp>> {
        if self.device.pair_state != PairState::Paired {
            return Err(anyhow!("Device must be paired to retrieve app list"));
        }

        info!("Getting app list from {}", self.device.name);

        let url = format!(
            "https://{}:{}/applist?uniqueid={}&uuid={}",
            self.device.address,
            self.device.https_port,
            self.unique_id,
            Uuid::new_v4().simple()
        );

        let response = self.client.get(&url).send().await?;
        let xml_content = response.text().await?;

        debug!("App list XML: {}", xml_content);

        let parser = XmlParser::new();
        parser.verify_response_status(&xml_content)?;

        let apps = parser.parse_app_list(&xml_content)?;
        info!("Retrieved {} apps from {}", apps.len(), self.device.name);

        Ok(apps)
    }

    pub async fn quit_app(&self) -> Result<()> {
        if self.device.pair_state != PairState::Paired {
            return Err(anyhow!("Device must be paired to quit app"));
        }

        info!("Quitting current app on {}", self.device.name);

        let url = format!(
            "https://{}:{}/cancel?uniqueid={}&uuid={}",
            self.device.address,
            self.device.https_port,
            self.unique_id,
            Uuid::new_v4().simple()
        );

        let response = self.client.get(&url).send().await?;
        let xml_content = response.text().await?;

        let parser = XmlParser::new();
        parser.verify_response_status(&xml_content)?;

        info!("Successfully quit app on {}", self.device.name);
        Ok(())
    }

    pub async fn launch_app(&self, app_id: u32) -> Result<String> {
        if self.device.pair_state != PairState::Paired {
            return Err(anyhow!("Device must be paired to launch app"));
        }

        info!("Launching app {} on {}", app_id, self.device.name);

        // Basic stream configuration for mouse-only mode
        let url = format!(
            "https://{}:{}/launch?uniqueid={}&uuid={}&appid={}&mode=1280x720x60&additionalStates=1&sops=1&rikey={}&rikeyid=1&localAudioPlayMode=0&surroundAudioInfo=0&remoteControllersBitmap=1&gcmap=1",
            self.device.address,
            self.device.https_port,
            self.unique_id,
            Uuid::new_v4().simple(),
            app_id,
            hex::encode(vec![0u8; 16]) // Dummy AES key for now
        );

        let response = self.client.get(&url).send().await?;
        let xml_content = response.text().await?;

        let parser = XmlParser::new();
        parser.verify_response_status(&xml_content)?;

        // Extract RTSP session URL if available
        let rtsp_url = parser.get_xml_string(&xml_content, "gamesession")
            .unwrap_or_default();

        info!("Successfully launched app {} on {}", app_id, self.device.name);
        Ok(rtsp_url)
    }

    pub fn get_device(&self) -> &GameStreamDevice {
        &self.device
    }

    pub fn get_device_mut(&mut self) -> &mut GameStreamDevice {
        &mut self.device
    }

    pub fn update_device(&mut self, device: GameStreamDevice) {
        self.device = device;
    }
}
