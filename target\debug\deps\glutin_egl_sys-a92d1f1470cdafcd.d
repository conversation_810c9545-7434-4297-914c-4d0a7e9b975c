C:\Users\<USER>\Downloads\moonlight-qt-master\moonlight-qt-master\target\debug\deps\libglutin_egl_sys-a92d1f1470cdafcd.rmeta: C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\glutin_egl_sys-0.5.1\src\lib.rs C:\Users\<USER>\Downloads\moonlight-qt-master\moonlight-qt-master\target\debug\build\glutin_egl_sys-d1cb1e24765c75d5\out/egl_bindings.rs

C:\Users\<USER>\Downloads\moonlight-qt-master\moonlight-qt-master\target\debug\deps\glutin_egl_sys-a92d1f1470cdafcd.d: C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\glutin_egl_sys-0.5.1\src\lib.rs C:\Users\<USER>\Downloads\moonlight-qt-master\moonlight-qt-master\target\debug\build\glutin_egl_sys-d1cb1e24765c75d5\out/egl_bindings.rs

C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\glutin_egl_sys-0.5.1\src\lib.rs:
C:\Users\<USER>\Downloads\moonlight-qt-master\moonlight-qt-master\target\debug\build\glutin_egl_sys-d1cb1e24765c75d5\out/egl_bindings.rs:

# env-dep:OUT_DIR=C:\\Users\\<USER>\\Downloads\\moonlight-qt-master\\moonlight-qt-master\\target\\debug\\build\\glutin_egl_sys-d1cb1e24765c75d5\\out
