{"rustc": 16591470773350601817, "features": "[\"alloc\", \"default\", \"dev_urandom_fallback\"]", "declared_features": "[\"alloc\", \"default\", \"dev_urandom_fallback\", \"less-safe-getrandom-custom-or-rdrand\", \"less-safe-getrandom-espidf\", \"slow_tests\", \"std\", \"test_logging\", \"unstable-testing-arm-no-hw\", \"unstable-testing-arm-no-neon\", \"wasm32_unknown_unknown_js\"]", "target": 5408242616063297496, "profile": 2225463790103693989, "path": 7231996106413311982, "deps": [[8413798824750015470, "cc", false, 12250171200233229766]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\ring-560a85aa87991d96\\dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}