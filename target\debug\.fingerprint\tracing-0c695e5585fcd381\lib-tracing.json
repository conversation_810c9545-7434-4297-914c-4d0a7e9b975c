{"rustc": 16591470773350601817, "features": "[\"attributes\", \"default\", \"std\", \"tracing-attributes\"]", "declared_features": "[\"async-await\", \"attributes\", \"default\", \"log\", \"log-always\", \"max_level_debug\", \"max_level_error\", \"max_level_info\", \"max_level_off\", \"max_level_trace\", \"max_level_warn\", \"release_max_level_debug\", \"release_max_level_error\", \"release_max_level_info\", \"release_max_level_off\", \"release_max_level_trace\", \"release_max_level_warn\", \"std\", \"tracing-attributes\", \"valuable\"]", "target": 5568135053145998517, "profile": 11202463608144111571, "path": 6405441750337901976, "deps": [[325572602735163265, "tracing_attributes", false, 11213231889318256957], [1906322745568073236, "pin_project_lite", false, 15035029367069786925], [3424551429995674438, "tracing_core", false, 12750079559313768152]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tracing-0c695e5585fcd381\\dep-lib-tracing", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}