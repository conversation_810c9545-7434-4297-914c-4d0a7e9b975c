{"rustc": 16591470773350601817, "features": "[\"alloc\", \"default\", \"dev_urandom_fallback\", \"once_cell\"]", "declared_features": "[\"alloc\", \"default\", \"dev_urandom_fallback\", \"internal_benches\", \"once_cell\", \"slow_tests\", \"std\", \"test_logging\", \"wasm32_c\"]", "target": 17591616432441575691, "profile": 2241668132362809309, "path": 8168292218897041691, "deps": [[2317793503723491507, "untrusted", false, 12453426439191158171], [3016319839805820069, "build_script_build", false, 11464691019852934442], [9009208741846480474, "spin", false, 8817976412413504964], [10020888071089587331, "<PERSON>ap<PERSON>", false, 1909546788331967060]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\ring-6534869221caa52a\\dep-lib-ring", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}