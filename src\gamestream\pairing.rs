use crate::gamestream::xml::XmlParser;
use crate::types::{GameStreamDevice, PairState};
use anyhow::{anyhow, Result};
use log::{debug, error, info, warn};
use reqwest::{Client, ClientBuilder};
use ring::rand::SystemRandom;
use ring::{aead, digest, pbkdf2};
use std::num::NonZeroU32;
use std::time::Duration;
use uuid::Uuid;

pub struct PairingManager {
    client: Client,
    device: GameStreamDevice,
    unique_id: String,
    salt: Vec<u8>,
}

impl PairingManager {
    pub fn new(device: GameStreamDevice) -> Result<Self> {
        let client = ClientBuilder::new()
            .timeout(Duration::from_secs(30))
            .danger_accept_invalid_certs(true)
            .build()?;

        let unique_id = "0123456789ABCDEF".to_string();
        
        // Generate random salt for key derivation
        let rng = SystemRandom::new();
        let mut salt = vec![0u8; 16];
        ring::rand::SecureRandom::fill(&rng, &mut salt)
            .map_err(|_| anyhow!("Failed to generate random salt"))?;

        Ok(Self {
            client,
            device,
            unique_id,
            salt,
        })
    }

    pub async fn pair_with_pin(&mut self, pin: &str) -> Result<PairState> {
        info!("Starting pairing process with device {}", self.device.name);

        // Step 1: Get server certificate and start pairing
        let server_cert = self.get_server_certificate().await?;
        
        // Step 2: Generate AES key from PIN
        let aes_key = self.derive_key_from_pin(pin)?;
        
        // Step 3: Send client challenge
        let pair_state = self.send_client_challenge(&aes_key).await?;
        if pair_state != PairState::Paired {
            return Ok(pair_state);
        }

        // Step 4: Complete pairing challenge
        let final_state = self.complete_pairing_challenge(&aes_key).await?;
        
        if final_state == PairState::Paired {
            self.device.pair_state = PairState::Paired;
            self.device.server_cert = Some(server_cert);
            info!("Successfully paired with device {}", self.device.name);
        }

        Ok(final_state)
    }

    async fn get_server_certificate(&self) -> Result<Vec<u8>> {
        info!("Getting server certificate from {}", self.device.address);

        let url = format!(
            "http://{}:{}/pair?uniqueid={}&uuid={}&devicename=gamestream-mouse-controller&updateState=1&phrase=getservercert",
            self.device.address,
            self.device.port,
            self.unique_id,
            Uuid::new_v4().simple()
        );

        let response = self.client.get(&url).send().await?;
        let xml_content = response.text().await?;

        let parser = XmlParser::new();
        parser.verify_response_status(&xml_content)?;

        // Extract server certificate
        if let Some(cert_hex) = parser.get_xml_string(&xml_content, "plaincert") {
            let cert_bytes = hex::decode(cert_hex)
                .map_err(|e| anyhow!("Failed to decode server certificate: {}", e))?;
            Ok(cert_bytes)
        } else {
            Err(anyhow!("Server certificate not found in response"))
        }
    }

    fn derive_key_from_pin(&self, pin: &str) -> Result<Vec<u8>> {
        let mut key = vec![0u8; 32]; // 256-bit key
        
        pbkdf2::derive(
            pbkdf2::PBKDF2_HMAC_SHA256,
            NonZeroU32::new(1000).unwrap(),
            &self.salt,
            pin.as_bytes(),
            &mut key,
        );

        Ok(key)
    }

    async fn send_client_challenge(&self, aes_key: &[u8]) -> Result<PairState> {
        info!("Sending client challenge");

        // Generate random challenge
        let rng = SystemRandom::new();
        let mut challenge = vec![0u8; 16];
        ring::rand::SecureRandom::fill(&rng, &mut challenge)
            .map_err(|_| anyhow!("Failed to generate challenge"))?;

        // Encrypt challenge
        let encrypted_challenge = self.encrypt_data(&challenge, aes_key)?;

        let url = format!(
            "http://{}:{}/pair?uniqueid={}&uuid={}&devicename=gamestream-mouse-controller&updateState=1&clientchallenge={}",
            self.device.address,
            self.device.port,
            self.unique_id,
            Uuid::new_v4().simple(),
            hex::encode(encrypted_challenge)
        );

        let response = self.client.get(&url).send().await?;
        let xml_content = response.text().await?;

        let parser = XmlParser::new();
        
        // Check if pairing was successful
        match parser.verify_response_status(&xml_content) {
            Ok(_) => {
                if let Some(paired) = parser.get_xml_string(&xml_content, "paired") {
                    match paired.as_str() {
                        "1" => Ok(PairState::Paired),
                        _ => Ok(PairState::Failed),
                    }
                } else {
                    Ok(PairState::Failed)
                }
            }
            Err(_) => {
                // Check for specific error messages
                if xml_content.contains("PIN") || xml_content.contains("pin") {
                    Ok(PairState::PinWrong)
                } else {
                    Ok(PairState::Failed)
                }
            }
        }
    }

    async fn complete_pairing_challenge(&self, _aes_key: &[u8]) -> Result<PairState> {
        info!("Completing pairing challenge");

        let url = format!(
            "https://{}:{}/pair?uniqueid={}&uuid={}&devicename=gamestream-mouse-controller&updateState=1&phrase=pairchallenge",
            self.device.address,
            self.device.https_port,
            self.unique_id,
            Uuid::new_v4().simple()
        );

        let response = self.client.get(&url).send().await?;
        let xml_content = response.text().await?;

        let parser = XmlParser::new();
        parser.verify_response_status(&xml_content)?;

        if let Some(paired) = parser.get_xml_string(&xml_content, "paired") {
            match paired.as_str() {
                "1" => Ok(PairState::Paired),
                _ => Ok(PairState::Failed),
            }
        } else {
            Ok(PairState::Failed)
        }
    }

    fn encrypt_data(&self, data: &[u8], key: &[u8]) -> Result<Vec<u8>> {
        let key = aead::UnboundKey::new(&aead::AES_256_GCM, key)
            .map_err(|_| anyhow!("Failed to create AES key"))?;
        let key = aead::LessSafeKey::new(key);

        let rng = SystemRandom::new();
        let mut nonce_bytes = vec![0u8; 12];
        ring::rand::SecureRandom::fill(&rng, &mut nonce_bytes)
            .map_err(|_| anyhow!("Failed to generate nonce"))?;
        
        let nonce = aead::Nonce::assume_unique_for_key(nonce_bytes.try_into().unwrap());

        let mut in_out = data.to_vec();
        key.seal_in_place_append_tag(nonce, aead::Aad::empty(), &mut in_out)
            .map_err(|_| anyhow!("Failed to encrypt data"))?;

        // Prepend nonce to encrypted data
        let mut result = nonce_bytes.clone();
        result.extend_from_slice(&in_out);
        Ok(result)
    }

    pub fn get_device(&self) -> &GameStreamDevice {
        &self.device
    }

    pub fn get_device_mut(&mut self) -> &mut GameStreamDevice {
        &mut self.device
    }
}
