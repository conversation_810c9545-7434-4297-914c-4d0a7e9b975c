{"rustc": 16591470773350601817, "features": "[\"__rustls\", \"__tls\", \"default\", \"default-tls\", \"hyper-rustls\", \"hyper-tls\", \"json\", \"native-tls-crate\", \"rustls\", \"rustls-tls\", \"rustls-tls-webpki-roots\", \"serde_json\", \"tokio-native-tls\", \"tokio-rustls\", \"webpki-roots\"]", "declared_features": "[\"__internal_proxy_sys_no_cache\", \"__rustls\", \"__tls\", \"async-compression\", \"blocking\", \"brotli\", \"cookie_crate\", \"cookie_store\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"futures-channel\", \"gzip\", \"h3\", \"h3-quinn\", \"hickory-dns\", \"hickory-resolver\", \"http3\", \"hyper-rustls\", \"hyper-tls\", \"json\", \"mime_guess\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-crate\", \"native-tls-vendored\", \"quinn\", \"rustls\", \"rustls-native-certs\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"serde_json\", \"socks\", \"stream\", \"tokio-native-tls\", \"tokio-rustls\", \"tokio-socks\", \"tokio-util\", \"trust-dns\", \"wasm-streams\", \"webpki-roots\"]", "target": 16585426341985349207, "profile": 2241668132362809309, "path": 11085906522254514755, "deps": [[40386456601120721, "percent_encoding", false, 9187242204290025924], [95042085696191081, "ipnet", false, 15052846331957786617], [264090853244900308, "sync_wrapper", false, 16334909456024987250], [784494742817713399, "tower_service", false, 12650781755870478975], [1044435446100926395, "hyper_rustls", false, 13068208317977520140], [1906322745568073236, "pin_project_lite", false, 15035029367069786925], [3150220818285335163, "url", false, 16905575937989364559], [3722963349756955755, "once_cell", false, 4343645139131842619], [4405182208873388884, "http", false, 11188495995216052676], [5986029879202738730, "log", false, 1928244995240463600], [7414427314941361239, "hyper", false, 13674415660245565888], [7620660491849607393, "futures_core", false, 2837511519693940984], [8405603588346937335, "winreg", false, 9200065359813868541], [8915503303801890683, "http_body", false, 17568006900548390100], [9689903380558560274, "serde", false, 5338506785776164340], [10229185211513642314, "mime", false, 5588091235973442811], [10629569228670356391, "futures_util", false, 17093309161738785981], [11295624341523567602, "rustls", false, 9004720006078605376], [12186126227181294540, "tokio_native_tls", false, 17067892225572218293], [12367227501898450486, "hyper_tls", false, 12917920708554301955], [12393800526703971956, "tokio", false, 1778925993519534301], [13763625454224483636, "h2", false, 9552783782255255048], [14564311161534545801, "encoding_rs", false, 14974422712059482091], [15367738274754116744, "serde_json", false, 6700137319367331833], [16066129441945555748, "bytes", false, 7669780274896816031], [16311359161338405624, "rustls_pemfile", false, 1055111481361216934], [16542808166767769916, "serde_urlencoded", false, 17356441465520728038], [16622232390123975175, "tokio_rustls", false, 14464317291875053604], [16785601910559813697, "native_tls_crate", false, 6100452917272844794], [17652733826348741533, "webpki_roots", false, 9225377807316383135], [18066890886671768183, "base64", false, 12080137752654402823]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\reqwest-1494358422a7a337\\dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}