{"rustc": 16591470773350601817, "features": "[\"alloc\", \"derive\"]", "declared_features": "[\"alloc\", \"default\", \"derive\"]", "target": 723370850876025358, "profile": 2241668132362809309, "path": 10706338726198688832, "deps": [[4022439902832367970, "zerofrom_derive", false, 13561574695610301626]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\zerofrom-9fe513dfe0994bde\\dep-lib-zerofrom", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}