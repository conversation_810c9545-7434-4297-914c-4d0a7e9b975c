use crate::types::GameStreamApp;
use anyhow::{anyhow, Result};
use log::debug;
use quick_xml::events::Event;
use quick_xml::Reader;

pub struct XmlParser;

impl XmlParser {
    pub fn new() -> Self {
        Self
    }

    pub fn verify_response_status(&self, xml: &str) -> Result<()> {
        if let Some(status) = self.get_xml_string(xml, "status") {
            if status != "200" {
                let status_message = self.get_xml_string(xml, "statusmessage")
                    .unwrap_or_else(|| "Unknown error".to_string());
                return Err(anyhow!("Server returned error {}: {}", status, status_message));
            }
        }
        Ok(())
    }

    pub fn get_xml_string(&self, xml: &str, tag_name: &str) -> Option<String> {
        let mut reader = Reader::from_str(xml);
        reader.trim_text(true);

        let mut buf = Vec::new();
        let mut in_target_tag = false;

        loop {
            match reader.read_event_into(&mut buf) {
                Ok(Event::Start(ref e)) => {
                    if e.name().as_ref() == tag_name.as_bytes() {
                        in_target_tag = true;
                    }
                }
                Ok(Event::Text(e)) => {
                    if in_target_tag {
                        if let Ok(text) = e.unescape() {
                            return Some(text.to_string());
                        }
                    }
                }
                Ok(Event::End(ref e)) => {
                    if e.name().as_ref() == tag_name.as_bytes() {
                        in_target_tag = false;
                    }
                }
                Ok(Event::Eof) => break,
                Err(e) => {
                    debug!("Error parsing XML: {}", e);
                    break;
                }
                _ => {}
            }
            buf.clear();
        }

        None
    }

    pub fn parse_app_list(&self, xml: &str) -> Result<Vec<GameStreamApp>> {
        let mut reader = Reader::from_str(xml);
        reader.trim_text(true);

        let mut buf = Vec::new();
        let mut apps = Vec::new();
        let mut current_app: Option<GameStreamApp> = None;
        let mut current_tag = String::new();

        loop {
            match reader.read_event_into(&mut buf) {
                Ok(Event::Start(ref e)) => {
                    let tag_name = String::from_utf8_lossy(e.name().as_ref()).to_string();
                    current_tag = tag_name.clone();

                    if tag_name == "App" {
                        // Validate previous app before starting new one
                        if let Some(app) = current_app.take() {
                            if app.id != 0 && !app.name.is_empty() {
                                apps.push(app);
                            }
                        }
                        current_app = Some(GameStreamApp {
                            id: 0,
                            name: String::new(),
                            hdr_supported: false,
                            hidden: false,
                        });
                    }
                }
                Ok(Event::Text(e)) => {
                    if let Some(ref mut app) = current_app {
                        if let Ok(text) = e.unescape() {
                            match current_tag.as_str() {
                                "AppTitle" => app.name = text.to_string(),
                                "ID" => {
                                    if let Ok(id) = text.parse::<u32>() {
                                        app.id = id;
                                    }
                                }
                                "IsHdrSupported" => app.hdr_supported = text == "1",
                                _ => {}
                            }
                        }
                    }
                }
                Ok(Event::End(ref e)) => {
                    let tag_name = String::from_utf8_lossy(e.name().as_ref()).to_string();
                    if tag_name == "App" {
                        if let Some(app) = current_app.take() {
                            if app.id != 0 && !app.name.is_empty() {
                                apps.push(app);
                            }
                        }
                    }
                    current_tag.clear();
                }
                Ok(Event::Eof) => break,
                Err(e) => {
                    return Err(anyhow!("Error parsing app list XML: {}", e));
                }
                _ => {}
            }
            buf.clear();
        }

        // Don't forget the last app
        if let Some(app) = current_app {
            if app.id != 0 && !app.name.is_empty() {
                apps.push(app);
            }
        }

        Ok(apps)
    }
}

impl Default for XmlParser {
    fn default() -> Self {
        Self::new()
    }
}
