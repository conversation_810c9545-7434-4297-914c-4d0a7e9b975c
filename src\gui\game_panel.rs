use crate::gui::app::GameStreamApp;
use crate::input::MouseInputHandler;
use crate::types::GameStreamApp as GameApp;
use eframe::egui;

pub fn show_game_panel(
    ui: &mut egui::Ui,
    available_apps: &mut Vec<GameApp>,
    mouse_capture_enabled: &mut bool,
    mouse_handler: &MouseInputHandler,
    app: &mut GameStreamApp,
) {
    ui.group(|ui| {
        ui.heading("Game Management");

        // App list controls
        ui.horizontal(|ui| {
            if ui.button("Refresh App List").clicked() {
                app.load_app_list();
            }

            if ui.button("Quit Current App").clicked() {
                // This would call quit_app on the connected client
            }
        });

        ui.separator();

        // Available apps
        if available_apps.is_empty() {
            ui.label("No apps available. Make sure device is paired and connected.");
            ui.label("Click 'Refresh App List' to load available games.");
        } else {
            ui.label(format!("Available Apps ({}):", available_apps.len()));

            egui::ScrollArea::vertical()
                .max_height(200.0)
                .show(ui, |ui| {
                    for app_item in available_apps.iter() {
                        ui.group(|ui| {
                            ui.horizontal(|ui| {
                                ui.vertical(|ui| {
                                    ui.strong(&app_item.name);
                                    ui.label(format!("ID: {}", app_item.id));
                                    
                                    if app_item.hdr_supported {
                                        ui.colored_label(egui::Color32::BLUE, "HDR Supported");
                                    }
                                    
                                    if app_item.hidden {
                                        ui.colored_label(egui::Color32::GRAY, "Hidden");
                                    }
                                });

                                ui.separator();

                                ui.vertical(|ui| {
                                    if ui.button("Launch (Mouse Only)").clicked() {
                                        // This would launch the app in mouse-only mode
                                    }
                                });
                            });
                        });
                        ui.add_space(3.0);
                    }
                });
        }

        ui.separator();

        // Mouse input controls
        ui.group(|ui| {
            ui.heading("Mouse Input Control");

            // Connection status
            let input_connected = mouse_handler.is_connected();
            let (status_text, status_color) = if input_connected {
                ("Connected", egui::Color32::GREEN)
            } else {
                ("Not Connected", egui::Color32::RED)
            };
            
            ui.horizontal(|ui| {
                ui.label("Input Status:");
                ui.colored_label(status_color, status_text);
            });

            // Mouse capture toggle
            ui.horizontal(|ui| {
                ui.checkbox(mouse_capture_enabled, "Enable Mouse Capture");
                
                if *mouse_capture_enabled != mouse_handler.is_capture_enabled() {
                    mouse_handler.set_capture_enabled(*mouse_capture_enabled);
                }
            });

            ui.separator();

            // Test controls
            ui.label("Test Functions:");
            
            ui.horizontal(|ui| {
                if ui.button("Test Mouse Movement").clicked() {
                    app.test_mouse_movement();
                }

                if ui.button("Test Mouse Click").clicked() {
                    app.test_mouse_click();
                }
            });

            // Manual movement controls
            ui.separator();
            ui.label("Manual Movement:");
            
            ui.horizontal(|ui| {
                if ui.button("⬅️ Left").clicked() {
                    if let Err(e) = mouse_handler.inject_mouse_movement(-10, 0) {
                        // Handle error
                    }
                }
                
                if ui.button("➡️ Right").clicked() {
                    if let Err(e) = mouse_handler.inject_mouse_movement(10, 0) {
                        // Handle error
                    }
                }
                
                if ui.button("⬆️ Up").clicked() {
                    if let Err(e) = mouse_handler.inject_mouse_movement(0, -10) {
                        // Handle error
                    }
                }
                
                if ui.button("⬇️ Down").clicked() {
                    if let Err(e) = mouse_handler.inject_mouse_movement(0, 10) {
                        // Handle error
                    }
                }
            });

            // Advanced controls
            ui.separator();
            ui.collapsing("Advanced Controls", |ui| {
                ui.label("Custom Movement:");
                
                static mut DELTA_X: i32 = 0;
                static mut DELTA_Y: i32 = 0;
                
                ui.horizontal(|ui| {
                    ui.label("Delta X:");
                    unsafe {
                        ui.add(egui::DragValue::new(&mut DELTA_X).range(-100..=100));
                    }
                });
                
                ui.horizontal(|ui| {
                    ui.label("Delta Y:");
                    unsafe {
                        ui.add(egui::DragValue::new(&mut DELTA_Y).range(-100..=100));
                    }
                });
                
                if ui.button("Send Custom Movement").clicked() {
                    unsafe {
                        if let Err(e) = mouse_handler.inject_mouse_movement(DELTA_X as i16, DELTA_Y as i16) {
                            // Handle error
                        }
                    }
                }
            });
        });
    });
}
