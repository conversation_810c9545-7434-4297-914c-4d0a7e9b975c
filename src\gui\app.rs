use crate::discovery::DeviceDiscovery;
use crate::gamestream::{GameStreamClient, PairingManager};
use crate::gui::{device_panel, game_panel};
use crate::input::MouseInputHandler;
use crate::types::{GameStreamApp as GameApp, GameStreamDevice, PairState};
use eframe::egui;
use log::{error, info, warn};
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::runtime::Runtime;

pub struct GameStreamApp {
    // Core components
    runtime: Arc<Runtime>,
    device_discovery: DeviceDiscovery,
    mouse_handler: MouseInputHandler,
    
    // State
    discovered_devices: Vec<GameStreamDevice>,
    selected_device: Option<GameStreamDevice>,
    connected_device: Option<GameStreamClient>,
    available_apps: Vec<GameApp>,
    
    // UI state
    scanning: bool,
    last_scan: Option<Instant>,
    scan_status: String,
    pairing_pin: String,
    pairing_in_progress: bool,
    connection_status: String,
    mouse_capture_enabled: bool,
    
    // Error handling
    last_error: Option<String>,
}

impl GameStreamApp {
    pub fn new() -> Self {
        let runtime = Arc::new(
            Runtime::new().expect("Failed to create Tokio runtime")
        );

        Self {
            runtime,
            device_discovery: DeviceDiscovery::new(),
            mouse_handler: MouseInputHandler::new(),
            discovered_devices: Vec::new(),
            selected_device: None,
            connected_device: None,
            available_apps: Vec::new(),
            scanning: false,
            last_scan: None,
            scan_status: "Ready to scan".to_string(),
            pairing_pin: String::new(),
            pairing_in_progress: false,
            connection_status: "Not connected".to_string(),
            mouse_capture_enabled: false,
            last_error: None,
        }
    }

    pub fn scan_for_devices(&mut self) {
        if self.scanning {
            return;
        }

        self.scanning = true;
        self.scan_status = "Scanning for GameStream devices...".to_string();
        self.last_error = None;

        // For now, simulate device discovery with a mock device
        // In a real implementation, this would use the actual mDNS discovery
        self.discovered_devices = vec![
            GameStreamDevice {
                name: "Mock GameStream PC".to_string(),
                uuid: "mock-uuid-12345".to_string(),
                address: std::net::IpAddr::V4(std::net::Ipv4Addr::new(192, 168, 1, 100)),
                port: 47989,
                https_port: 47984,
                mac_address: Some("00:11:22:33:44:55".to_string()),
                state: crate::types::DeviceState::Online,
                pair_state: crate::types::PairState::NotPaired,
                current_game_id: None,
                apps: Vec::new(),
                server_cert: None,
            }
        ];

        self.last_scan = Some(Instant::now());
    }

    pub fn connect_to_device(&mut self, device: GameStreamDevice) {
        if self.connected_device.is_some() {
            self.disconnect_from_device();
        }

        self.connection_status = format!("Connecting to {}...", device.name);
        
        match GameStreamClient::new(device.clone()) {
            Ok(mut client) => {
                let runtime = Arc::clone(&self.runtime);
                
                // Get server info first
                runtime.spawn(async move {
                    match client.get_server_info().await {
                        Ok(_) => {
                            info!("Successfully connected to {}", client.get_device().name);
                        }
                        Err(e) => {
                            error!("Failed to get server info: {}", e);
                        }
                    }
                });
                
                self.selected_device = Some(device);
                self.connection_status = "Connected".to_string();
            }
            Err(e) => {
                self.last_error = Some(format!("Failed to connect: {}", e));
                self.connection_status = "Connection failed".to_string();
            }
        }
    }

    pub fn disconnect_from_device(&mut self) {
        if let Some(_) = self.connected_device.take() {
            self.mouse_handler.disconnect();
            self.available_apps.clear();
            self.connection_status = "Disconnected".to_string();
            info!("Disconnected from device");
        }
    }

    pub fn pair_with_device(&mut self, device: GameStreamDevice, pin: &str) {
        if self.pairing_in_progress {
            return;
        }

        self.pairing_in_progress = true;
        self.last_error = None;

        let runtime = Arc::clone(&self.runtime);
        let pin = pin.to_string();
        
        runtime.spawn(async move {
            match PairingManager::new(device) {
                Ok(mut pairing_manager) => {
                    match pairing_manager.pair_with_pin(&pin).await {
                        Ok(PairState::Paired) => {
                            info!("Successfully paired with device");
                        }
                        Ok(PairState::PinWrong) => {
                            warn!("Pairing failed: Wrong PIN");
                        }
                        Ok(state) => {
                            warn!("Pairing failed with state: {:?}", state);
                        }
                        Err(e) => {
                            error!("Pairing error: {}", e);
                        }
                    }
                }
                Err(e) => {
                    error!("Failed to create pairing manager: {}", e);
                }
            }
        });
    }

    pub fn load_app_list(&mut self) {
        if let Some(client) = &self.connected_device {
            let runtime = Arc::clone(&self.runtime);
            
            // Clone client for async operation
            // Note: In a real implementation, you'd need proper async handling
            runtime.spawn(async move {
                // This would need proper implementation with message passing
                // For now, this is a placeholder
            });
        }
    }

    pub fn test_mouse_movement(&mut self) {
        if let Err(e) = self.mouse_handler.send_test_mouse_movement() {
            self.last_error = Some(format!("Test movement failed: {}", e));
        }
    }

    pub fn test_mouse_click(&mut self) {
        if let Err(e) = self.mouse_handler.send_test_mouse_click() {
            self.last_error = Some(format!("Test click failed: {}", e));
        }
    }
}

impl eframe::App for GameStreamApp {
    fn update(&mut self, ctx: &egui::Context, _frame: &mut eframe::Frame) {
        // Update scanning status
        if self.scanning {
            if let Some(last_scan) = self.last_scan {
                if last_scan.elapsed() > Duration::from_secs(10) {
                    self.scanning = false;
                    self.scan_status = "Scan completed".to_string();
                }
            }
        }

        egui::CentralPanel::default().show(ctx, |ui| {
            ui.heading("GameStream Mouse Controller");
            
            ui.separator();

            // Error display
            if let Some(error) = &self.last_error {
                ui.colored_label(egui::Color32::RED, format!("Error: {}", error));
                if ui.button("Clear Error").clicked() {
                    self.last_error = None;
                }
                ui.separator();
            }

            // Device discovery panel
            device_panel::show_device_panel(
                ui,
                &mut self.discovered_devices,
                &mut self.selected_device,
                &mut self.scanning,
                &mut self.scan_status,
                &mut self.pairing_pin,
                &mut self.pairing_in_progress,
                &mut self.connection_status,
                self,
            );

            ui.separator();

            // Game panel (only show if connected)
            if self.selected_device.is_some() {
                game_panel::show_game_panel(
                    ui,
                    &mut self.available_apps,
                    &mut self.mouse_capture_enabled,
                    &self.mouse_handler,
                    self,
                );
            }
        });

        // Request repaint for animations
        ctx.request_repaint_after(Duration::from_millis(100));
    }
}
