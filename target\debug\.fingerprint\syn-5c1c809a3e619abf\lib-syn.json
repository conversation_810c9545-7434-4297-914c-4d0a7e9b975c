{"rustc": 16591470773350601817, "features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"visit\", \"visit-mut\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"test\", \"visit\", \"visit-mut\"]", "target": 9442126953582868550, "profile": 2225463790103693989, "path": 12328351182160161988, "deps": [[1988483478007900009, "unicode_ident", false, 14202206654413834917], [3060637413840920116, "proc_macro2", false, 1170828167349903917], [17990358020177143287, "quote", false, 10209240271242811793]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\syn-5c1c809a3e619abf\\dep-lib-syn", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}