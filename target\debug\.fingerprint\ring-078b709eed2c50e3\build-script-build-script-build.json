{"rustc": 16591470773350601817, "features": "[\"alloc\", \"default\", \"dev_urandom_fallback\", \"once_cell\"]", "declared_features": "[\"alloc\", \"default\", \"dev_urandom_fallback\", \"internal_benches\", \"once_cell\", \"slow_tests\", \"std\", \"test_logging\", \"wasm32_c\"]", "target": 17883862002600103897, "profile": 2225463790103693989, "path": 17521086628600094481, "deps": [[8413798824750015470, "cc", false, 12250171200233229766]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\ring-078b709eed2c50e3\\dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}