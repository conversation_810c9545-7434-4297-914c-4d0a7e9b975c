{"$message_type":"diagnostic","message":"unused imports: `error` and `warn`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\gamestream\\client.rs","byte_start":163,"byte_end":168,"line_start":4,"line_end":4,"column_start":18,"column_end":23,"is_primary":true,"text":[{"text":"use log::{debug, error, info, warn};","highlight_start":18,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\gamestream\\client.rs","byte_start":176,"byte_end":180,"line_start":4,"line_end":4,"column_start":31,"column_end":35,"is_primary":true,"text":[{"text":"use log::{debug, error, info, warn};","highlight_start":31,"highlight_end":35}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src\\gamestream\\client.rs","byte_start":161,"byte_end":168,"line_start":4,"line_end":4,"column_start":16,"column_end":23,"is_primary":true,"text":[{"text":"use log::{debug, error, info, warn};","highlight_start":16,"highlight_end":23}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\gamestream\\client.rs","byte_start":174,"byte_end":180,"line_start":4,"line_end":4,"column_start":29,"column_end":35,"is_primary":true,"text":[{"text":"use log::{debug, error, info, warn};","highlight_start":29,"highlight_end":35}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `error` and `warn`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\gamestream\\client.rs:4:18\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse log::{debug, error, info, warn};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `debug`, `error`, and `warn`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\gamestream\\pairing.rs","byte_start":128,"byte_end":133,"line_start":4,"line_end":4,"column_start":11,"column_end":16,"is_primary":true,"text":[{"text":"use log::{debug, error, info, warn};","highlight_start":11,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\gamestream\\pairing.rs","byte_start":135,"byte_end":140,"line_start":4,"line_end":4,"column_start":18,"column_end":23,"is_primary":true,"text":[{"text":"use log::{debug, error, info, warn};","highlight_start":18,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\gamestream\\pairing.rs","byte_start":148,"byte_end":152,"line_start":4,"line_end":4,"column_start":31,"column_end":35,"is_primary":true,"text":[{"text":"use log::{debug, error, info, warn};","highlight_start":31,"highlight_end":35}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src\\gamestream\\pairing.rs","byte_start":128,"byte_end":142,"line_start":4,"line_end":4,"column_start":11,"column_end":25,"is_primary":true,"text":[{"text":"use log::{debug, error, info, warn};","highlight_start":11,"highlight_end":25}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\gamestream\\pairing.rs","byte_start":146,"byte_end":152,"line_start":4,"line_end":4,"column_start":29,"column_end":35,"is_primary":true,"text":[{"text":"use log::{debug, error, info, warn};","highlight_start":29,"highlight_end":35}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\gamestream\\pairing.rs","byte_start":127,"byte_end":128,"line_start":4,"line_end":4,"column_start":10,"column_end":11,"is_primary":true,"text":[{"text":"use log::{debug, error, info, warn};","highlight_start":10,"highlight_end":11}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\gamestream\\pairing.rs","byte_start":152,"byte_end":153,"line_start":4,"line_end":4,"column_start":35,"column_end":36,"is_primary":true,"text":[{"text":"use log::{debug, error, info, warn};","highlight_start":35,"highlight_end":36}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `debug`, `error`, and `warn`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\gamestream\\pairing.rs:4:11\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse log::{debug, error, info, warn};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `digest`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\gamestream\\pairing.rs","byte_start":240,"byte_end":246,"line_start":7,"line_end":7,"column_start":18,"column_end":24,"is_primary":true,"text":[{"text":"use ring::{aead, digest, pbkdf2};","highlight_start":18,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\gamestream\\pairing.rs","byte_start":238,"byte_end":246,"line_start":7,"line_end":7,"column_start":16,"column_end":24,"is_primary":true,"text":[{"text":"use ring::{aead, digest, pbkdf2};","highlight_start":16,"highlight_end":24}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `digest`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\gamestream\\pairing.rs:7:18\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse ring::{aead, digest, pbkdf2};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `Enigo` and `MouseControllable`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\input\\mouse_handler.rs","byte_start":177,"byte_end":182,"line_start":4,"line_end":4,"column_start":13,"column_end":18,"is_primary":true,"text":[{"text":"use enigo::{Enigo, MouseControllable};","highlight_start":13,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\input\\mouse_handler.rs","byte_start":184,"byte_end":201,"line_start":4,"line_end":4,"column_start":20,"column_end":37,"is_primary":true,"text":[{"text":"use enigo::{Enigo, MouseControllable};","highlight_start":20,"highlight_end":37}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\input\\mouse_handler.rs","byte_start":165,"byte_end":204,"line_start":4,"line_end":5,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use enigo::{Enigo, MouseControllable};","highlight_start":1,"highlight_end":39},{"text":"use log::{debug, error, info, warn};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `Enigo` and `MouseControllable`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\input\\mouse_handler.rs:4:13\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse enigo::{Enigo, MouseControllable};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `debug` and `warn`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\input\\mouse_handler.rs","byte_start":214,"byte_end":219,"line_start":5,"line_end":5,"column_start":11,"column_end":16,"is_primary":true,"text":[{"text":"use log::{debug, error, info, warn};","highlight_start":11,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\input\\mouse_handler.rs","byte_start":234,"byte_end":238,"line_start":5,"line_end":5,"column_start":31,"column_end":35,"is_primary":true,"text":[{"text":"use log::{debug, error, info, warn};","highlight_start":31,"highlight_end":35}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src\\input\\mouse_handler.rs","byte_start":214,"byte_end":221,"line_start":5,"line_end":5,"column_start":11,"column_end":18,"is_primary":true,"text":[{"text":"use log::{debug, error, info, warn};","highlight_start":11,"highlight_end":18}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\input\\mouse_handler.rs","byte_start":232,"byte_end":238,"line_start":5,"line_end":5,"column_start":29,"column_end":35,"is_primary":true,"text":[{"text":"use log::{debug, error, info, warn};","highlight_start":29,"highlight_end":35}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `debug` and `warn`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\input\\mouse_handler.rs:5:11\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse log::{debug, error, info, warn};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\u001b[0m               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `EventType`, `Event`, and `listen`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\input\\mouse_handler.rs","byte_start":252,"byte_end":258,"line_start":6,"line_end":6,"column_start":12,"column_end":18,"is_primary":true,"text":[{"text":"use rdev::{listen, Event, EventType};","highlight_start":12,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\input\\mouse_handler.rs","byte_start":260,"byte_end":265,"line_start":6,"line_end":6,"column_start":20,"column_end":25,"is_primary":true,"text":[{"text":"use rdev::{listen, Event, EventType};","highlight_start":20,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\input\\mouse_handler.rs","byte_start":267,"byte_end":276,"line_start":6,"line_end":6,"column_start":27,"column_end":36,"is_primary":true,"text":[{"text":"use rdev::{listen, Event, EventType};","highlight_start":27,"highlight_end":36}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\input\\mouse_handler.rs","byte_start":241,"byte_end":279,"line_start":6,"line_end":7,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use rdev::{listen, Event, EventType};","highlight_start":1,"highlight_end":38},{"text":"use std::sync::mpsc::{self, Receiver, Sender};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `EventType`, `Event`, and `listen`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\input\\mouse_handler.rs:6:12\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m6\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse rdev::{listen, Event, EventType};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `protocol::InputProtocol`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\input\\mod.rs","byte_start":84,"byte_end":107,"line_start":5,"line_end":5,"column_start":9,"column_end":32,"is_primary":true,"text":[{"text":"pub use protocol::InputProtocol;","highlight_start":9,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\input\\mod.rs","byte_start":76,"byte_end":109,"line_start":5,"line_end":5,"column_start":1,"column_end":34,"is_primary":true,"text":[{"text":"pub use protocol::InputProtocol;","highlight_start":1,"highlight_end":34}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `protocol::InputProtocol`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\input\\mod.rs:5:9\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use protocol::InputProtocol;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `range` found for struct `egui::DragValue` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\gui\\game_panel.rs","byte_start":5429,"byte_end":5434,"line_start":153,"line_end":153,"column_start":67,"column_end":72,"is_primary":true,"text":[{"text":"                        ui.add(egui::DragValue::new(&mut DELTA_X).range(-100..=100));","highlight_start":67,"highlight_end":72}],"label":"method not found in `DragValue<'_>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no method named `range` found for struct `egui::DragValue` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\gui\\game_panel.rs:153:67\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m153\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                        ui.add(egui::DragValue::new(&mut DELTA_X).range(-100..=100));\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmethod not found in `DragValue<'_>`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `range` found for struct `egui::DragValue` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\gui\\game_panel.rs","byte_start":5682,"byte_end":5687,"line_start":160,"line_end":160,"column_start":67,"column_end":72,"is_primary":true,"text":[{"text":"                        ui.add(egui::DragValue::new(&mut DELTA_Y).range(-100..=100));","highlight_start":67,"highlight_end":72}],"label":"method not found in `DragValue<'_>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no method named `range` found for struct `egui::DragValue` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\gui\\game_panel.rs:160:67\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m160\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                        ui.add(egui::DragValue::new(&mut DELTA_Y).range(-100..=100));\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmethod not found in `DragValue<'_>`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"borrow of moved value: `nonce_bytes`","code":{"code":"E0382","explanation":"A variable was used after its contents have been moved elsewhere.\n\nErroneous code example:\n\n```compile_fail,E0382\nstruct MyStruct { s: u32 }\n\nfn main() {\n    let mut x = MyStruct{ s: 5u32 };\n    let y = x;\n    x.s = 6;\n    println!(\"{}\", x.s);\n}\n```\n\nSince `MyStruct` is a type that is not marked `Copy`, the data gets moved out\nof `x` when we set `y`. This is fundamental to Rust's ownership system: outside\nof workarounds like `Rc`, a value cannot be owned by more than one variable.\n\nSometimes we don't need to move the value. Using a reference, we can let another\nfunction borrow the value without changing its ownership. In the example below,\nwe don't actually have to move our string to `calculate_length`, we can give it\na reference to it with `&` instead.\n\n```\nfn main() {\n    let s1 = String::from(\"hello\");\n\n    let len = calculate_length(&s1);\n\n    println!(\"The length of '{}' is {}.\", s1, len);\n}\n\nfn calculate_length(s: &String) -> usize {\n    s.len()\n}\n```\n\nA mutable reference can be created with `&mut`.\n\nSometimes we don't want a reference, but a duplicate. All types marked `Clone`\ncan be duplicated by calling `.clone()`. Subsequent changes to a clone do not\naffect the original variable.\n\nMost types in the standard library are marked `Clone`. The example below\ndemonstrates using `clone()` on a string. `s1` is first set to \"many\", and then\ncopied to `s2`. Then the first character of `s1` is removed, without affecting\n`s2`. \"any many\" is printed to the console.\n\n```\nfn main() {\n    let mut s1 = String::from(\"many\");\n    let s2 = s1.clone();\n    s1.remove(0);\n    println!(\"{} {}\", s1, s2);\n}\n```\n\nIf we control the definition of a type, we can implement `Clone` on it ourselves\nwith `#[derive(Clone)]`.\n\nSome types have no ownership semantics at all and are trivial to duplicate. An\nexample is `i32` and the other number types. We don't have to call `.clone()` to\nclone them, because they are marked `Copy` in addition to `Clone`. Implicit\ncloning is more convenient in this case. We can mark our own types `Copy` if\nall their members also are marked `Copy`.\n\nIn the example below, we implement a `Point` type. Because it only stores two\nintegers, we opt-out of ownership semantics with `Copy`. Then we can\n`let p2 = p1` without `p1` being moved.\n\n```\n#[derive(Copy, Clone)]\nstruct Point { x: i32, y: i32 }\n\nfn main() {\n    let mut p1 = Point{ x: -1, y: 2 };\n    let p2 = p1;\n    p1.x = 1;\n    println!(\"p1: {}, {}\", p1.x, p1.y);\n    println!(\"p2: {}, {}\", p2.x, p2.y);\n}\n```\n\nAlternatively, if we don't control the struct's definition, or mutable shared\nownership is truly required, we can use `Rc` and `RefCell`:\n\n```\nuse std::cell::RefCell;\nuse std::rc::Rc;\n\nstruct MyStruct { s: u32 }\n\nfn main() {\n    let mut x = Rc::new(RefCell::new(MyStruct{ s: 5u32 }));\n    let y = x.clone();\n    x.borrow_mut().s = 6;\n    println!(\"{}\", x.borrow().s);\n}\n```\n\nWith this approach, x and y share ownership of the data via the `Rc` (reference\ncount type). `RefCell` essentially performs runtime borrow checking: ensuring\nthat at most one writer or multiple readers can access the data at any one time.\n\nIf you wish to learn more about ownership in Rust, start with the\n[Understanding Ownership][understanding-ownership] chapter in the Book.\n\n[understanding-ownership]: https://doc.rust-lang.org/book/ch04-00-understanding-ownership.html\n"},"level":"error","spans":[{"file_name":"src\\gamestream\\pairing.rs","byte_start":6735,"byte_end":6745,"line_start":196,"line_end":196,"column_start":68,"column_end":78,"is_primary":false,"text":[{"text":"        let nonce = aead::Nonce::assume_unique_for_key(nonce_bytes.try_into().unwrap());","highlight_start":68,"highlight_end":78}],"label":"`nonce_bytes` moved due to this method call","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\gamestream\\pairing.rs","byte_start":7006,"byte_end":7017,"line_start":203,"line_end":203,"column_start":26,"column_end":37,"is_primary":true,"text":[{"text":"        let mut result = nonce_bytes.clone();","highlight_start":26,"highlight_end":37}],"label":"value borrowed here after move","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\gamestream\\pairing.rs","byte_start":6499,"byte_end":6514,"line_start":192,"line_end":192,"column_start":13,"column_end":28,"is_primary":false,"text":[{"text":"        let mut nonce_bytes = vec![0u8; 12];","highlight_start":13,"highlight_end":28}],"label":"move occurs because `nonce_bytes` has type `std::vec::Vec<u8>`, which does not implement the `Copy` trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`try_into` takes ownership of the receiver `self`, which moves `nonce_bytes`","code":null,"level":"note","spans":[{"file_name":"C:\\Users\\<USER>\\.rustup\\toolchains\\stable-x86_64-pc-windows-msvc\\lib/rustlib/src/rust\\library\\core\\src\\convert\\mod.rs","byte_start":23692,"byte_end":23696,"line_start":613,"line_end":613,"column_start":17,"column_end":21,"is_primary":true,"text":[{"text":"    fn try_into(self) -> Result<T, Self::Error>;","highlight_start":17,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"you can `clone` the value and consume it, but this might not be your desired behavior","code":null,"level":"help","spans":[{"file_name":"src\\gamestream\\pairing.rs","byte_start":6734,"byte_end":6734,"line_start":196,"line_end":196,"column_start":67,"column_end":67,"is_primary":true,"text":[{"text":"        let nonce = aead::Nonce::assume_unique_for_key(nonce_bytes.try_into().unwrap());","highlight_start":67,"highlight_end":67}],"label":null,"suggested_replacement":".clone()","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0382]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: borrow of moved value: `nonce_bytes`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\gamestream\\pairing.rs:203:26\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m192\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let mut nonce_bytes = vec![0u8; 12];\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m---------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mmove occurs because `nonce_bytes` has type `std::vec::Vec<u8>`, which does not implement the `Copy` trait\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m196\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let nonce = aead::Nonce::assume_unique_for_key(nonce_bytes.try_into().unwrap());\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`nonce_bytes` moved due to this method call\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m203\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let mut result = nonce_bytes.clone();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mvalue borrowed here after move\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: `try_into` takes ownership of the receiver `self`, which moves `nonce_bytes`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.rustup\\toolchains\\stable-x86_64-pc-windows-msvc\\lib/rustlib/src/rust\\library\\core\\src\\convert\\mod.rs:613:17\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m613\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn try_into(self) -> Result<T, Self::Error>;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: you can `clone` the value and consume it, but this might not be your desired behavior\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m196\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m        let nonce = aead::Nonce::assume_unique_for_key(nonce_bytes\u001b[0m\u001b[0m\u001b[38;5;10m.clone()\u001b[0m\u001b[0m.try_into().unwrap());\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                   \u001b[0m\u001b[0m\u001b[38;5;10m++++++++\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `client`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\gui\\app.rs","byte_start":5784,"byte_end":5790,"line_start":171,"line_end":171,"column_start":21,"column_end":27,"is_primary":true,"text":[{"text":"        if let Some(client) = &self.connected_device {","highlight_start":21,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\gui\\app.rs","byte_start":5784,"byte_end":5790,"line_start":171,"line_end":171,"column_start":21,"column_end":27,"is_primary":true,"text":[{"text":"        if let Some(client) = &self.connected_device {","highlight_start":21,"highlight_end":27}],"label":null,"suggested_replacement":"_client","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `client`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\gui\\app.rs:171:21\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m171\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        if let Some(client) = &self.connected_device {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_client`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"cannot borrow `*self` as mutable more than once at a time","code":{"code":"E0499","explanation":"A variable was borrowed as mutable more than once.\n\nErroneous code example:\n\n```compile_fail,E0499\nlet mut i = 0;\nlet mut x = &mut i;\nlet mut a = &mut i;\nx;\n// error: cannot borrow `i` as mutable more than once at a time\n```\n\nPlease note that in Rust, you can either have many immutable references, or one\nmutable reference. For more details you may want to read the\n[References & Borrowing][references-and-borrowing] section of the Book.\n\n[references-and-borrowing]: https://doc.rust-lang.org/book/ch04-02-references-and-borrowing.html\n\nExample:\n\n```\nlet mut i = 0;\nlet mut x = &mut i; // ok!\n\n// or:\nlet mut i = 0;\nlet a = &i; // ok!\nlet b = &i; // still ok!\nlet c = &i; // still ok!\nb;\na;\n```\n"},"level":"error","spans":[{"file_name":"src\\gui\\app.rs","byte_start":7674,"byte_end":7702,"line_start":225,"line_end":225,"column_start":17,"column_end":45,"is_primary":false,"text":[{"text":"                &mut self.discovered_devices,","highlight_start":17,"highlight_end":45}],"label":"first mutable borrow occurs here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\gui\\app.rs","byte_start":7969,"byte_end":7973,"line_start":232,"line_end":232,"column_start":17,"column_end":21,"is_primary":true,"text":[{"text":"                self,","highlight_start":17,"highlight_end":21}],"label":"second mutable borrow occurs here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\gui\\app.rs","byte_start":7605,"byte_end":7636,"line_start":223,"line_end":223,"column_start":13,"column_end":44,"is_primary":false,"text":[{"text":"            device_panel::show_device_panel(","highlight_start":13,"highlight_end":44}],"label":"first borrow later used by call","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0499]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: cannot borrow `*self` as mutable more than once at a time\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\gui\\app.rs:232:17\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m223\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            device_panel::show_device_panel(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mfirst borrow later used by call\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m224\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                ui,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m225\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                &mut self.discovered_devices,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m----------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mfirst mutable borrow occurs here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m232\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                self,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9msecond mutable borrow occurs here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"cannot borrow `*self` as mutable more than once at a time","code":{"code":"E0499","explanation":"A variable was borrowed as mutable more than once.\n\nErroneous code example:\n\n```compile_fail,E0499\nlet mut i = 0;\nlet mut x = &mut i;\nlet mut a = &mut i;\nx;\n// error: cannot borrow `i` as mutable more than once at a time\n```\n\nPlease note that in Rust, you can either have many immutable references, or one\nmutable reference. For more details you may want to read the\n[References & Borrowing][references-and-borrowing] section of the Book.\n\n[references-and-borrowing]: https://doc.rust-lang.org/book/ch04-02-references-and-borrowing.html\n\nExample:\n\n```\nlet mut i = 0;\nlet mut x = &mut i; // ok!\n\n// or:\nlet mut i = 0;\nlet a = &i; // ok!\nlet b = &i; // still ok!\nlet c = &i; // still ok!\nb;\na;\n```\n"},"level":"error","spans":[{"file_name":"src\\gui\\app.rs","byte_start":8208,"byte_end":8232,"line_start":241,"line_end":241,"column_start":21,"column_end":45,"is_primary":false,"text":[{"text":"                    &mut self.available_apps,","highlight_start":21,"highlight_end":45}],"label":"first mutable borrow occurs here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\gui\\app.rs","byte_start":8348,"byte_end":8352,"line_start":244,"line_end":244,"column_start":21,"column_end":25,"is_primary":true,"text":[{"text":"                    self,","highlight_start":21,"highlight_end":25}],"label":"second mutable borrow occurs here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\gui\\app.rs","byte_start":8135,"byte_end":8162,"line_start":239,"line_end":239,"column_start":17,"column_end":44,"is_primary":false,"text":[{"text":"                game_panel::show_game_panel(","highlight_start":17,"highlight_end":44}],"label":"first borrow later used by call","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0499]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: cannot borrow `*self` as mutable more than once at a time\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\gui\\app.rs:244:21\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m239\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                game_panel::show_game_panel(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m---------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mfirst borrow later used by call\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m240\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    ui,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m241\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    &mut self.available_apps,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mfirst mutable borrow occurs here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m244\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    self,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9msecond mutable borrow occurs here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"cannot borrow `*self` as mutable because it is also borrowed as immutable","code":{"code":"E0502","explanation":"A variable already borrowed with a certain mutability (either mutable or\nimmutable) was borrowed again with a different mutability.\n\nErroneous code example:\n\n```compile_fail,E0502\nfn bar(x: &mut i32) {}\nfn foo(a: &mut i32) {\n    let y = &a; // a is borrowed as immutable.\n    bar(a); // error: cannot borrow `*a` as mutable because `a` is also borrowed\n            //        as immutable\n    println!(\"{}\", y);\n}\n```\n\nTo fix this error, ensure that you don't have any other references to the\nvariable before trying to access it with a different mutability:\n\n```\nfn bar(x: &mut i32) {}\nfn foo(a: &mut i32) {\n    bar(a);\n    let y = &a; // ok!\n    println!(\"{}\", y);\n}\n```\n\nFor more information on Rust's ownership system, take a look at the\n[References & Borrowing][references-and-borrowing] section of the Book.\n\n[references-and-borrowing]: https://doc.rust-lang.org/book/ch04-02-references-and-borrowing.html\n"},"level":"error","spans":[{"file_name":"src\\input\\mouse_handler.rs","byte_start":1354,"byte_end":1383,"line_start":38,"line_end":38,"column_start":9,"column_end":38,"is_primary":true,"text":[{"text":"        self.start_input_processing()?;","highlight_start":9,"highlight_end":38}],"label":"mutable borrow occurs here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\input\\mouse_handler.rs","byte_start":1207,"byte_end":1226,"line_start":34,"line_end":34,"column_start":28,"column_end":47,"is_primary":false,"text":[{"text":"        let mut protocol = self.input_protocol.lock().unwrap();","highlight_start":28,"highlight_end":47}],"label":"immutable borrow occurs here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\input\\mouse_handler.rs","byte_start":1475,"byte_end":1476,"line_start":42,"line_end":42,"column_start":5,"column_end":6,"is_primary":false,"text":[{"text":"    }","highlight_start":5,"highlight_end":6}],"label":"immutable borrow might be used here, when `protocol` is dropped and runs the `Drop` code for type `std::sync::MutexGuard`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0502]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: cannot borrow `*self` as mutable because it is also borrowed as immutable\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\input\\mouse_handler.rs:38:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m34\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let mut protocol = self.input_protocol.lock().unwrap();\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mimmutable borrow occurs here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m38\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        self.start_input_processing()?;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmutable borrow occurs here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m42\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    }\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mimmutable borrow might be used here, when `protocol` is dropped and runs the `Drop` code for type `std::sync::MutexGuard`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 6 previous errors; 8 warnings emitted","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: aborting due to 6 previous errors; 8 warnings emitted\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"Some errors have detailed explanations: E0382, E0499, E0502, E0599.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;15mSome errors have detailed explanations: E0382, E0499, E0502, E0599.\u001b[0m\n"}
{"$message_type":"diagnostic","message":"For more information about an error, try `rustc --explain E0382`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;15mFor more information about an error, try `rustc --explain E0382`.\u001b[0m\n"}
